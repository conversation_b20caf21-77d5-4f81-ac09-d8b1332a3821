// ✅ Task 99: Agent Terminal Integration Bridge
// Provides agents with real-time terminal command execution capabilities

export interface AgentTerminalCommandRequest {
  command: string;
  agentId: string;
  sessionId?: string;
  timeout?: number; // milliseconds, default 30000
  workingDirectory?: string;
  environment?: Record<string, string>;
}

export interface AgentTerminalCommandResponse {
  success: boolean;
  output: string;
  error?: string;
  exitCode?: number;
  executionTime: number;
  sessionId: string;
  agentId: string;
  command: string;
}

export interface TerminalSession {
  id: string;
  agentId?: string;
  isShared: boolean;
  createdAt: number;
  lastUsed: number;
  commandCount: number;
  workingDirectory: string;
}

class AgentTerminalBridge {
  private activeSessions: Map<string, TerminalSession> = new Map();
  private defaultSharedSessionId: string | null = null;
  private commandHistory: Map<string, AgentTerminalCommandRequest[]> = new Map();
  private maxHistoryPerAgent = 100;

  /**
   * ✅ Task 99 Step 2: Execute command via agent terminal API
   */
  async executeCommand(request: AgentTerminalCommandRequest): Promise<AgentTerminalCommandResponse> {
    const startTime = Date.now();
    
    try {
      // Validate request
      this.validateCommandRequest(request);
      
      // Get or create session
      const sessionId = await this.getOrCreateSession(request.sessionId, request.agentId);
      
      // Log command to history
      this.addToCommandHistory(request.agentId, { ...request, sessionId });
      
      // Execute via Electron IPC
      const result = await this.executeViaIPC(request, sessionId);
      
      // Update session usage
      this.updateSessionUsage(sessionId);
      
      const executionTime = Date.now() - startTime;
      
      const response: AgentTerminalCommandResponse = {
        success: result.success,
        output: result.output || '',
        error: result.error,
        exitCode: result.exitCode,
        executionTime,
        sessionId,
        agentId: request.agentId,
        command: request.command
      };
      
      console.log(`✅ AgentTerminalBridge: Command executed for ${request.agentId} in ${executionTime}ms`);
      return response;
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`❌ AgentTerminalBridge: Command failed for ${request.agentId}:`, error);
      
      return {
        success: false,
        output: '',
        error: errorMessage,
        executionTime,
        sessionId: request.sessionId || 'unknown',
        agentId: request.agentId,
        command: request.command
      };
    }
  }

  /**
   * ✅ Task 99: Get or create terminal session for agent
   */
  private async getOrCreateSession(sessionId?: string, agentId?: string): Promise<string> {
    // If specific session requested, validate it exists
    if (sessionId) {
      if (this.activeSessions.has(sessionId)) {
        return sessionId;
      } else {
        throw new Error(`Terminal session ${sessionId} not found`);
      }
    }
    
    // Use default shared session if no specific session
    if (!this.defaultSharedSessionId) {
      this.defaultSharedSessionId = await this.createSharedSession();
    }
    
    return this.defaultSharedSessionId;
  }

  /**
   * ✅ Task 99: Create shared agent terminal session
   */
  private async createSharedSession(): Promise<string> {
    const sessionId = `agent-shared-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const session: TerminalSession = {
      id: sessionId,
      isShared: true,
      createdAt: Date.now(),
      lastUsed: Date.now(),
      commandCount: 0,
      workingDirectory: process.cwd()
    };
    
    this.activeSessions.set(sessionId, session);
    console.log(`✅ AgentTerminalBridge: Created shared session ${sessionId}`);
    
    return sessionId;
  }

  /**
   * ✅ Task 99: Create dedicated session for specific agent
   */
  async createAgentSession(agentId: string, workingDirectory?: string): Promise<string> {
    const sessionId = `agent-${agentId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const session: TerminalSession = {
      id: sessionId,
      agentId,
      isShared: false,
      createdAt: Date.now(),
      lastUsed: Date.now(),
      commandCount: 0,
      workingDirectory: workingDirectory || process.cwd()
    };
    
    this.activeSessions.set(sessionId, session);
    console.log(`✅ AgentTerminalBridge: Created dedicated session ${sessionId} for agent ${agentId}`);
    
    return sessionId;
  }

  /**
   * ✅ Task 99: Execute command via Electron IPC
   */
  private async executeViaIPC(request: AgentTerminalCommandRequest, sessionId: string): Promise<any> {
    if (typeof window === 'undefined' || !window.electronAPI?.terminal?.agentCommand) {
      throw new Error('Terminal API not available - not running in Electron environment');
    }
    
    const ipcRequest = {
      command: request.command,
      agentId: request.agentId,
      sessionId,
      timeout: request.timeout || 30000,
      workingDirectory: request.workingDirectory,
      environment: request.environment
    };
    
    return await window.electronAPI.terminal.agentCommand(ipcRequest);
  }

  /**
   * ✅ Task 99: Validate command request
   */
  private validateCommandRequest(request: AgentTerminalCommandRequest): void {
    if (!request.command || typeof request.command !== 'string') {
      throw new Error('Command must be a non-empty string');
    }
    
    if (!request.agentId || typeof request.agentId !== 'string') {
      throw new Error('AgentId must be a non-empty string');
    }
    
    if (request.timeout && (typeof request.timeout !== 'number' || request.timeout <= 0)) {
      throw new Error('Timeout must be a positive number');
    }
  }

  /**
   * ✅ Task 99: Update session usage tracking
   */
  private updateSessionUsage(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.lastUsed = Date.now();
      session.commandCount++;
    }
  }

  /**
   * ✅ Task 99: Add command to agent history
   */
  private addToCommandHistory(agentId: string, request: AgentTerminalCommandRequest): void {
    if (!this.commandHistory.has(agentId)) {
      this.commandHistory.set(agentId, []);
    }
    
    const history = this.commandHistory.get(agentId)!;
    history.push(request);
    
    // Limit history size
    if (history.length > this.maxHistoryPerAgent) {
      history.splice(0, history.length - this.maxHistoryPerAgent);
    }
  }

  /**
   * ✅ Task 99: Get command history for agent
   */
  getCommandHistory(agentId: string): AgentTerminalCommandRequest[] {
    return this.commandHistory.get(agentId) || [];
  }

  /**
   * ✅ Task 99: Get active sessions
   */
  getActiveSessions(): TerminalSession[] {
    return Array.from(this.activeSessions.values());
  }

  /**
   * ✅ Task 99: Get session info
   */
  getSessionInfo(sessionId: string): TerminalSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * ✅ Task 99: Close session
   */
  closeSession(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      this.activeSessions.delete(sessionId);
      
      // If this was the default shared session, clear it
      if (sessionId === this.defaultSharedSessionId) {
        this.defaultSharedSessionId = null;
      }
      
      console.log(`✅ AgentTerminalBridge: Closed session ${sessionId}`);
      return true;
    }
    return false;
  }

  /**
   * ✅ Task 99: Cleanup old sessions
   */
  cleanupOldSessions(maxAgeMs: number = 3600000): number { // 1 hour default
    const now = Date.now();
    let cleaned = 0;
    
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now - session.lastUsed > maxAgeMs && !session.isShared) {
        this.closeSession(sessionId);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      console.log(`✅ AgentTerminalBridge: Cleaned up ${cleaned} old sessions`);
    }
    
    return cleaned;
  }
}

// Export singleton instance
export const agentTerminalBridge = new AgentTerminalBridge();

/**
 * ✅ Task 99 Step 2: Convenience function for agent command execution
 */
export async function agentExecuteCommand(
  agentId: string, 
  command: string, 
  sessionId?: string,
  options?: {
    timeout?: number;
    workingDirectory?: string;
    environment?: Record<string, string>;
  }
): Promise<AgentTerminalCommandResponse> {
  return await agentTerminalBridge.executeCommand({
    command,
    agentId,
    sessionId,
    timeout: options?.timeout,
    workingDirectory: options?.workingDirectory,
    environment: options?.environment
  });
}
